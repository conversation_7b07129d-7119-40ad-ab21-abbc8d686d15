/*
  Theme Name: TheEvent
  Theme URL: https://bootstrapmade.com/theevent-conference-event-bootstrap-template/
  Main JS File <PERSON>
  Author: BootstrapMade.com
  License: https://bootstrapmade.com/license/
*/

/*--------------------------------------------------------------
# General
--------------------------------------------------------------*/

body {
  background: #fff;
  color: #2f3138;
  font-family: "Open Sans", sans-serif;
}

a {
  color: #f82249;
  transition: 0.5s;
}

a:hover,
a:active,
a:focus {
  color: #f8234a;
  outline: none;
  text-decoration: none;
}

p {
  padding: 0;
  margin: 0 0 30px 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: "Raleway", sans-serif;
  font-weight: 400;
  margin: 0 0 20px 0;
  padding: 0;
  color: #0e1b4d;
}

.main-page {
  margin-top: 70px;
}

.wow {
  visibility: hidden;
}

/* Prelaoder */

#preloader {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  overflow: visible;
  background: #fff url("../img/preloader.svg") no-repeat center center;
}

/* Back to top button */

.back-to-top {
  position: fixed;
  display: none;
  background: #f82249;
  color: #fff;
  width: 40px;
  height: 40px;
  text-align: center;
  border-radius: 50px;
  right: 15px;
  bottom: 15px;
  transition: background 0.5s ease-in-out;
}

.back-to-top i {
  font-size: 24px;
  padding-top: 6px;
}

.back-to-top:focus {
  background: #e0072f;
  color: #fff;
  outline: none;
}

.back-to-top:hover {
  background: #e0072f;
  color: #fff;
}

/* Sections Header
--------------------------------*/

.section-header {
  margin-bottom: 60px;
  position: relative;
  padding-bottom: 20px;
}

.section-header::before {
  content: '';
  position: absolute;
  display: block;
  width: 60px;
  height: 5px;
  background: #f82249;
  bottom: 0;
  left: calc(50% - 25px);
}

.section-header h2 {
  font-size: 36px;
  text-transform: uppercase;
  text-align: center;
  font-weight: 700;
  margin-bottom: 10px;
}

.section-header p {
  text-align: center;
  padding: 0;
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #9195a2;
}

.section-with-bg {
  background-color: #f6f7fd;
}

/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/

#header {
  background-color: rgba(255, 255, 255, 0.98);
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  transition: all 0.5s;
  z-index: 997;
  display: contents;
  flex-direction: column;
}

#header .container {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
}

#header.header-scrolled,
#header.header-fixed {
  /* background: rgba(6, 12, 34, 0.98); */
  background: rgba(255, 255, 255, 0.98);
  transition: all 0.5s;
}

#header #logo {
  padding: 1rem 0;
}

#header #logo h1 {
  font-size: 36px;
  margin: 0;
  padding: 6px 0;
  line-height: 1;
  font-family: "Raleway", sans-serif;
  font-weight: 700;
  letter-spacing: 3px;
  text-transform: uppercase;
}

#header #logo h1 span {
  color: #f82249;
}

#header #logo h1 a,
#header #logo h1 a:hover {
  color: #fff;
}

#header #logo img {
  padding: 0;
  margin: 0;
  max-height: 100px;
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/

/* Nav Menu Essentials */

.nav-menu,
.nav-menu * {
  margin: 0;
  padding: 0;
  list-style: none;
}

.nav-menu ul {
  position: absolute;
  display: none;
  top: 100%;
  left: 0;
  z-index: 99;
}

.nav-menu li {
  position: relative;
  white-space: nowrap;
}

.nav-menu > li {
  float: left;
}

.nav-menu li:hover > ul,
.nav-menu li.sfHover > ul {
  display: block;
}

.nav-menu ul ul {
  top: 0;
  left: 100%;
}

.nav-menu ul li {
  min-width: 180px;
}

/* Nav Menu Arrows */

.sf-arrows .sf-with-ul {
  padding-right: 0;
}

.sf-arrows .sf-with-ul:after {
  display: none;
}

.sf-arrows ul .sf-with-ul:after {
  display: none;
}

/* Nav Meu Container */
#nav-menu-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
}

.nav-menu {
  display: flex;
  list-style: none;
}

.nav-menu li {
  padding: 0 15px;
}

.nav-menu a {
  color: #2f3138;
  text-decoration: none;
  font-weight: 600;
  font-size: large;
}

.nav-menu .menu-active a,
.nav-menu a:hover {
  color: #f82249;
}

.nav-menu > li {
  margin-left: 8px;
}

.nav-menu > li > a:before {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: #f82249;
  visibility: hidden;
  transition: all 0.3s ease-in-out 0s;
}

.nav-menu a:hover:before,
.nav-menu li:hover > a:before,
.nav-menu .menu-active > a:before {
  visibility: visible;
  width: 100%;
}

.nav-menu li.buy-tickets a {
  color: #fff;
  background: #f82249;
  padding: 7px 22px;
  border-radius: 50px;
  border: 2px solid #f82249;
  transition: all ease-in-out 0.3s;
  font-weight: 500;
  margin-left: 8px;
  margin-top: 2px;
  line-height: 1;
  font-size: 13px;
}

.nav-menu li.buy-tickets a:hover {
  background: none;
}

.nav-menu li.buy-tickets:hover a:before,
.nav-menu li.buy-tickets.menu-active a:before {
  visibility: hidden;
}

.nav-menu ul {
  margin: 0;
  padding: 10px;
  box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.25);
  background: #fff;
  border-radius: 3px;
}

.nav-menu ul li {
  transition: 0.3s;
}

.nav-menu ul li a {
  padding: 10px;
  color: #060c22;
  transition: 0.3s;
  display: block;
  font-size: 13px;
  text-transform: none;
  border-radius: 3px;
}

.nav-menu ul li:hover > a {
  background: #f82249;
  color: #fff;
}

.nav-menu {
  display: flex;
  list-style: none;
}

.nav-menu li {
  padding: 0 15px;
}

.nav-menu a {
  color: #2f3138;
  text-decoration: none;
  font-weight: 600;
}

/* Mobile Nav Toggle */

#mobile-nav-toggle {
  position: fixed;
  right: 0;
  top: 0;
  z-index: 999;
  margin: 15px 15px 0 0;
  border: 0;
  background: none;
  font-size: 24px;
  display: none;
  transition: all 0.4s;
  outline: none;
  cursor: pointer;
}

#mobile-nav-toggle i {
  color: #fff;
}

/* Mobile Nav Styling */

#mobile-nav {
  position: fixed;
  top: 0;
  padding-top: 18px;
  bottom: 0;
  z-index: 998;
  background: rgba(6, 12, 34, 0.9);
  left: -260px;
  width: 260px;
  overflow-y: auto;
  transition: 0.4s;
}

#mobile-nav ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

#mobile-nav ul li {
  position: relative;
}

#mobile-nav ul li a {
  color: #fff;
  font-size: 17px;
  overflow: hidden;
  padding: 10px 22px 10px 15px;
  position: relative;
  text-decoration: none;
  width: 100%;
  display: block;
  outline: none;
}

#mobile-nav ul li a:hover {
  color: #f82249;
}

#mobile-nav ul li li {
  padding-left: 30px;
}

#mobile-nav ul .menu-has-children i {
  position: absolute;
  right: 0;
  z-index: 99;
  padding: 15px;
  cursor: pointer;
  color: #fff;
}

#mobile-nav ul .menu-has-children i.fa-chevron-up {
  color: #f82249;
}

#mobile-nav ul .menu-item-active {
  color: #f82249;
}

#mobile-body-overly {
  width: 100%;
  height: 100%;
  z-index: 997;
  top: 0;
  left: 0;
  position: fixed;
  background: rgba(6, 12, 34, 0.8);
  display: none;
}

/* Mobile Nav body classes */

body.mobile-nav-active {
  overflow: hidden;
}

body.mobile-nav-active #mobile-nav {
  left: 0;
}

body.mobile-nav-active #mobile-nav-toggle {
  color: #fff;
}

/*--------------------------------------------------------------
# Intro Section
--------------------------------------------------------------*/

#intro {
  width: 100%;
  height: 100vh;
  /* background: url(../img/intro-bg.jpg) top center; */
  background: url(../img/ioi2025-full.png) top center;
  background-size: cover;
  overflow: hidden;
  position: relative;
}

#intro:before {
  content: "";
  background: rgba(6, 12, 34, 0.8);
  position: absolute;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
}

#intro .intro-container {
  position: absolute;
  bottom: 0;
  left: 0;
  top: 300px;
  right: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  text-align: center;
  padding: 0 15px;
}

#intro h1 {
  color: #fff;
  font-family: "Raleway", sans-serif;
  font-size: 56px;
  font-weight: 600;
  text-transform: uppercase;
}

#intro h1 span {
  color: #f82249;
}

#intro p {
  color: #ebebeb;
  font-weight: 700;
  font-size: 20px;
}

#intro .play-btn {
  width: 94px;
  height: 94px;
  background: radial-gradient(#f82249 50%, rgba(101, 111, 150, 0.15) 52%);
  border-radius: 50%;
  display: block;
  position: relative;
  overflow: hidden;
}

#intro .play-btn::after {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translateX(-40%) translateY(-50%);
  transform: translateX(-40%) translateY(-50%);
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 15px solid #fff;
  z-index: 100;
  transition: all 400ms cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

#intro .play-btn:before {
  content: '';
  position: absolute;
  width: 120px;
  height: 120px;
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
  -webkit-animation: pulsate-btn 2s;
  animation: pulsate-btn 2s;
  -webkit-animation-direction: forwards;
  animation-direction: forwards;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: steps;
  animation-timing-function: steps;
  opacity: 1;
  border-radius: 50%;
  border: 2px solid rgba(163, 163, 163, 0.4);
  top: -15%;
  left: -15%;
  background: rgba(198, 16, 0, 0);
}

#intro .play-btn:hover::after {
  border-left: 15px solid #f82249;
  -webkit-transform: scale(20);
  transform: scale(20);
}

#intro .play-btn:hover::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translateX(-40%) translateY(-50%);
  transform: translateX(-40%) translateY(-50%);
  width: 0;
  height: 0;
  border: none;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 15px solid #fff;
  z-index: 200;
  -webkit-animation: none;
  animation: none;
  border-radius: 0;
}

#intro .about-btn {
  font-family: "Raleway", sans-serif;
  font-weight: 500;
  font-size: 14px;
  letter-spacing: 1px;
  display: inline-block;
  padding: 12px 32px;
  border-radius: 50px;
  transition: 0.5s;
  line-height: 1;
  margin: 10px;
  color: #fff;
  -webkit-animation-delay: 0.8s;
  animation-delay: 0.8s;
  border: 2px solid #f82249;
}

#intro .about-btn:hover {
  background: #f82249;
  color: #fff;
}

.text-white {
  color: #fff !important;
}

@-webkit-keyframes pulsate-btn {
  0% {
    -webkit-transform: scale(0.6, 0.6);
    transform: scale(0.6, 0.6);
    opacity: 1;
  }

  100% {
    -webkit-transform: scale(1, 1);
    transform: scale(1, 1);
    opacity: 0;
  }
}

@keyframes pulsate-btn {
  0% {
    -webkit-transform: scale(0.6, 0.6);
    transform: scale(0.6, 0.6);
    opacity: 1;
  }

  100% {
    -webkit-transform: scale(1, 1);
    transform: scale(1, 1);
    opacity: 0;
  }
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/

#contest {
  background: url("../img/sucre.jpg");
  background-size: cover;
  overflow: hidden;
  position: relative;
  color: #fff;
  padding: 60px 0 40px 0;
}

#contest:before {
  content: "";
  background: rgba(13, 20, 41, 0.8);
  position: absolute;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
}

#contest h2 {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #fff;
}

#contest h3 {
  font-size: 18px;
  font-weight: bold;
  text-transform: uppercase;
  margin-bottom: 10px;
  color: #fff;
}

#contest p {
  font-size: 14px;
  margin-bottom: 20px;
  color: #fff;
}

/*--------------------------------------------------------------
# Speakers Section
--------------------------------------------------------------*/

#bolivia {
  padding: 60px 0 30px 0;
}

#bolivia .speaker {
  position: relative;
  overflow: hidden;
  margin-bottom: 30px;
}

#bolivia .speaker .details {
  background: rgba(6, 12, 34, 0.76);
  position: absolute;
  left: 0;
  bottom: -30px;
  right: 0;
  text-align: center;
  padding-top: 10px;
  transition: all 300ms cubic-bezier(0.645, 0.045, 0.355, 1);
}

#bolivia .speaker .details h3 {
  color: #fff;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 5px;
}

#bolivia .speaker .details p {
  color: #fff;
  font-size: 15px;
  margin-bottom: 10px;
  font-style: italic;
}

#bolivia .speaker .details .social {
  height: 30px;
}

#bolivia .speaker .details a {
  color: #fff;
}

#bolivia .speaker .details a:hover {
  color: #f82249;
}

#bolivia .speaker:hover .details {
  bottom: 0;
}

#bolivia>div>div.section-header p {
  /* background-color: #00ff00; */
  text-align: justify;

  padding: 15px;
}

/* #footer-logo {
  background-color: #fff;
} */

#speakers-details {
  padding: 60px 0;
}

#speakers-details .details h2 {
  color: #112363;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 10px;
}

#speakers-details .details .social {
  margin-bottom: 15px;
}

#speakers-details .details .social a {
  background: #e9edfb;
  color: #112363;
  line-height: 1;
  display: inline-block;
  text-align: center;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  padding-top: 9px;
}

#speakers-details .details .social a:hover {
  background: #f82249;
  color: #fff;
}

#speakers-details .details .social a i {
  font-size: 18px;
}

#speakers-details .details p {
  color: #112363;
  font-size: 15px;
  margin-bottom: 10px;
}

/*--------------------------------------------------------------
# Schedule Section
--------------------------------------------------------------*/

#schedule {
  padding: 60px 0 60px 0;
}

#schedule .nav-tabs {
  text-align: center;
  margin: auto;
  display: block;
  border-bottom: 0;
  margin-bottom: 30px;
}

#schedule .nav-tabs li {
  display: inline-block;
  margin-bottom: 0;
}

#schedule .nav-tabs a {
  border: none;
  border-radius: 50px;
  font-weight: 600;
  background-color: #0e1b4d;
  color: #fff;
  padding: 10px 100px;
}

#schedule .nav-tabs a.active {
  background-color: #f82249;
  color: #fff;
}

#schedule .sub-heading {
  text-align: center;
  font-size: 18px;
  font-style: italic;
  margin: 0 auto 30px auto;
}

#schedule .tab-pane {
  transition: ease-in-out .2s;
}

#schedule .schedule-item {
  border-bottom: 1px solid #cad4f6;
  padding-top: 15px;
  padding-bottom: 15px;
  transition: background-color ease-in-out 0.3s;
}

#schedule .schedule-item:hover {
  background-color: #fff;
}

#schedule .schedule-item time {
  padding-bottom: 5px;
  display: inline-block;
}

#schedule .schedule-item .speaker {
  width: 60px;
  height: 60px;
  overflow: hidden;
  border-radius: 50%;
  float: left;
  margin: 0 10px 10px 0;
}

#schedule .schedule-item .speaker img {
  height: 100%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  margin-left: 50%;
  transition: all ease-in-out 0.3s;
}

#schedule .schedule-item h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

#schedule .schedule-item h4 span {
  font-style: italic;
  color: #19328e;
  font-weight: normal;
  font-size: 16px;
}

#schedule .schedule-item p {
  font-style: italic;
  color: #152b79;
  margin-bottom: 0;
}

/*--------------------------------------------------------------
# Venue Section
--------------------------------------------------------------*/

#venue {
  padding: 60px 0;
}

#venue .container-fluid {
  margin-bottom: 3px;
}

#venue .venue-map iframe {
  width: 100%;
  height: 100%;
  min-height: 300px;
}

#venue .venue-info {
  background: url("../img/venue-info-bg.jpg") top center no-repeat;
  background-size: cover;
  position: relative;
  padding-top: 60px;
  padding-bottom: 60px;
}

#venue .venue-info:before {
  content: "";
  background: rgba(13, 20, 41, 0.8);
  position: absolute;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
}

#venue .venue-info h3 {
  font-size: 36px;
  font-weight: 700;
  color: #fff;
}

#venue .venue-info p {
  color: #fff;
  margin-bottom: 0;
}

#venue .venue-gallery-container {
  padding-right: 12px;
}

#venue .venue-gallery {
  overflow: hidden;
  border-right: 3px solid #fff;
  border-bottom: 3px solid #fff;
}

#venue .venue-gallery img {
  transition: all ease-in-out 0.4s;
}

#venue .venue-gallery:hover img {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

/*--------------------------------------------------------------
# Hotels Section
--------------------------------------------------------------*/

#hotels {
  padding: 60px 0;
}

#hotels .hotel {
  border: 1px solid #e0e5fa;
  background: #fff;
  margin-bottom: 30px;
}

#hotels .hotel:hover .hotel-img img {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

#hotels .hotel-img {
  overflow: hidden;
  margin-bottom: 15px;
}

#hotels .hotel-img img {
  transition: 0.3s ease-in-out;
}

#hotels h3 {
  font-weight: 600;
  font-size: 20px;
  margin-bottom: 5px;
  padding: 0 20px;
}

#hotels a {
  color: #152b79;
}

#hotels a:hover {
  color: #f82249;
}

#hotels .stars {
  padding: 0 20px;
  margin-bottom: 5px;
}

#hotels .stars i {
  color: #ffc31d;
}

#hotels p {
  padding: 0 20px;
  margin-bottom: 20px;
  color: #060c22;
  font-style: italic;
  font-size: 15px;
}

/*--------------------------------------------------------------
# Gallery Section
--------------------------------------------------------------*/

#gallery {
  padding: 60px;
  overflow: hidden;
}

#gallery .owl-nav,
#gallery .owl-dots {
  margin-top: 25px;
  text-align: center;
}

#gallery .owl-item {
  border-left: 2px solid #fff;
  border-right: 2px solid #fff;
}

#gallery .owl-dot {
  display: inline-block;
  margin: 0 5px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #ddd;
}

#gallery .owl-dot.active {
  background-color: #f82249;
}

#gallery .gallery-carousel .owl-stage-outer {
  overflow: visible;
}

#gallery .gallery-carousel .center {
  border: 6px solid #f82249;
  margin: -10px;
  box-sizing: content-box;
  padding: 4px;
  background: #fff;
  z-index: 1;
}

/*--------------------------------------------------------------
# supporters Section
--------------------------------------------------------------*/

#supporters {
  padding: 60px 0;
}

#supporters .supporters-wrap {
  border-top: 1px solid #e0e5fa;
  border-left: 1px solid #e0e5fa;
  margin-bottom: 30px;
}

#supporters .supporter-logo {
  padding: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  border-right: 1px solid #e0e5fa;
  border-bottom: 1px solid #e0e5fa;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.5);
  height: 160px;
}

#supporters .supporter-logo:hover img {
  -webkit-transform: scale(1.2);
  transform: scale(1.2);
}

#supporters img {
  transition: all 0.4s ease-in-out;
}

/*--------------------------------------------------------------
# F.A.Q Section
--------------------------------------------------------------*/

#faq {
  padding: 60px 0;
}

#faq #faq-list {
  padding: 0;
  list-style: none;
}

#faq #faq-list li {
  border-bottom: 1px solid #ddd;
}

#faq #faq-list a {
  padding: 18px 0;
  display: block;
  position: relative;
  font-family: "Raleway", sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 600;
  padding-right: 20px;
}

#faq #faq-list i {
  font-size: 24px;
  position: absolute;
  right: 0;
  top: 16px;
}

#faq #faq-list p {
  margin-bottom: 20px;
}

#faq #faq-list a {
  color: inherit !important;
  text-decoration: none !important;
  cursor: pointer;
}

#faq #faq-list a.collapsed,
#faq #faq-list a.collapse,
#faq #faq-list a.collapsed:hover,
#faq #faq-list a.collapse:hover,
#faq #faq-list a.collapsed:focus,
#faq #faq-list a.collapse:focus {
  color: inherit !important;
}

#faq #faq-list a.collapsed i::before {
  content: "\f055" !important;
}

/*--------------------------------------------------------------
# Subscribe Section
--------------------------------------------------------------*/

#subscribe {
  padding: 60px;
  background: url(../img/subscribe-bg.jpg) center center no-repeat;
  background-size: cover;
  overflow: hidden;
  position: relative;
}

#subscribe:before {
  content: "";
  background: rgba(6, 12, 34, 0.6);
  position: absolute;
  bottom: 0;
  top: 0;
  left: 0;
  right: 0;
}

#subscribe .section-header h2,
#subscribe p {
  color: #fff;
}

#subscribe input {
  background: #fff;
  color: #060c22;
  border: 0;
  outline: none;
  margin: 0;
  padding: 9px 20px;
  border-radius: 50px;
  font-size: 14px;
}

#subscribe button {
  border: 0;
  padding: 9px 25px;
  cursor: pointer;
  background: #f82249;
  color: #fff;
  transition: all 0.3s ease;
  outline: none;
  font-size: 14px;
  border-radius: 50px;
}

#subscribe button:hover {
  background: #e0072f;
}

/*--------------------------------------------------------------
# Buy Tickets Section
--------------------------------------------------------------*/

#buy-tickets {
  padding: 60px 0;
}

#buy-tickets .card {
  border: none;
  border-radius: 5px;
  transition: all  0.3s ease-in-out;
  box-shadow: 0 10px 25px 0 rgba(6, 12, 34, 0.1);
}

#buy-tickets .card:hover {
  box-shadow: 0 10px 35px 0 rgba(6, 12, 34, 0.2);
}

#buy-tickets .card hr {
  margin: 25px 0;
}

#buy-tickets .card .card-title {
  margin: 10px 0;
  font-size: 14px;
  letter-spacing: 1px;
  font-weight: bold;
}

#buy-tickets .card .card-price {
  font-size: 48px;
  margin: 0;
}

#buy-tickets .card ul li {
  margin-bottom: 20px;
}

#buy-tickets .card .text-muted {
  opacity: 0.7;
}

#buy-tickets .card .btn {
  font-size: 15px;
  border-radius: 50px;
  padding: 10px 40px;
  transition: all 0.2s;
  background-color: #f82249;
  border: 0;
  color: #fff;
}

#buy-tickets .card .btn:hover {
  background-color: #e0072f;
}

#buy-tickets #buy-ticket-modal input,
#buy-tickets #buy-ticket-modal select {
  border-radius: 0;
}

#buy-tickets #buy-ticket-modal .btn {
  font-size: 15px;
  border-radius: 50px;
  padding: 10px 40px;
  transition: all 0.2s;
  background-color: #f82249;
  border: 0;
  color: #fff;
}

#buy-tickets #buy-ticket-modal .btn:hover {
  background-color: #e0072f;
}

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/

#contact {
  padding: 60px 0;
}

#contact .contact-info {
  margin-bottom: 20px;
  text-align: center;
}

#contact .contact-info i {
  font-size: 48px;
  display: inline-block;
  margin-bottom: 10px;
  color: #f82249;
}

#contact .contact-info address,
#contact .contact-info p {
  margin-bottom: 0;
  color: #112363;
}

#contact .contact-info h3 {
  font-size: 18px;
  margin-bottom: 15px;
  font-weight: bold;
  text-transform: uppercase;
  color: #112363;
}

#contact .contact-info a {
  color: #4869df;
}

#contact .contact-info a:hover {
  color: #f82249;
}

#contact .contact-address,
#contact .contact-phone,
#contact .contact-email {
  margin-bottom: 20px;
}

#contact .form #sendmessage {
  color: #f82249;
  border: 1px solid #f82249;
  display: none;
  text-align: center;
  padding: 15px;
  font-weight: 600;
  margin-bottom: 15px;
}

#contact .form #errormessage {
  color: red;
  display: none;
  border: 1px solid red;
  text-align: center;
  padding: 15px;
  font-weight: 600;
  margin-bottom: 15px;
}

#contact .form #sendmessage.show,
#contact .form #errormessage.show,
#contact .form .show {
  display: block;
}

#contact .form .validation {
  color: red;
  display: none;
  margin: 0 0 20px;
  font-weight: 400;
  font-size: 13px;
}

#contact .form input,
#contact .form textarea {
  padding: 10px 14px;
  border-radius: 0;
  box-shadow: none;
  font-size: 15px;
}

#contact .form button[type="submit"] {
  background: #f82249;
  border: 0;
  padding: 10px 40px;
  color: #fff;
  transition: 0.4s;
  border-radius: 50px;
  cursor: pointer;
}

#contact .form button[type="submit"]:hover {
  background: #e0072f;
}

.call4tasks {
  margin-top: 100px;
}

.call4tasks > .container > .section-header {
  --font-family-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  font-size: 18px;
}

.call4tasks > .container > .section-header > p{
  text-align: justify;
  padding-bottom: 15px;
  color: #343a40;
}

/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/

#footer {
  background: #101522;
  padding: 0 0 25px 0;
  color: #eee;
  font-size: 14px;
}

#footer .footer-top {
  background: #232c42;
  padding: 60px 0 30px 0;
}

#footer .footer-top .footer-info {
  margin-bottom: 30px;
}

#footer .footer-top .footer-info h3 {
  font-size: 26px;
  margin: 0 0 20px 0;
  padding: 2px 0 2px 0;
  line-height: 1;
  font-family: "Raleway", sans-serif;
  font-weight: 700;
  color: #fff;
}

#footer .footer-top .footer-info img {
  height: 40px;
  margin-bottom: 10px;
}

#footer .footer-top .footer-info p {
  font-size: 14px;
  line-height: 24px;
  margin-bottom: 0;
  font-family: "Raleway", sans-serif;
  color: #fff;
}

#footer .footer-top .social-links a {
  font-size: 18px;
  display: inline-block;
  background: #222636;
  color: #eee;
  line-height: 1;
  padding: 8px 0;
  margin-right: 4px;
  border-radius: 50%;
  text-align: center;
  width: 36px;
  height: 36px;
  transition: 0.3s;
}

#footer .footer-top .social-links a:hover {
  background: #f82249;
  color: #fff;
}

#footer .footer-top h4 {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  text-transform: uppercase;
  position: relative;
  padding-bottom: 12px;
  border-bottom: 2px solid #f82249;
}

#footer .footer-top .footer-links {
  margin-bottom: 30px;
}

#footer .footer-top .footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

#footer .footer-top .footer-links ul i {
  padding-right: 5px;
  color: #f82249;
  font-size: 18px;
}

#footer .footer-top .footer-links ul li {
  border-bottom: 1px solid #262c44;
  padding: 10px 0;
}

#footer .footer-top .footer-links ul li:first-child {
  padding-top: 0;
}

#footer .footer-top .footer-links ul a {
  color: #eee;
}

#footer .footer-top .footer-links ul a:hover {
  color: #f82249;
}

#footer .footer-top .footer-contact {
  margin-bottom: 30px;
}

#footer .footer-top .footer-contact p {
  line-height: 26px;
}

#footer .footer-top .footer-newsletter {
  margin-bottom: 30px;
}

#footer .footer-top .footer-newsletter input[type="email"] {
  border: 0;
  padding: 6px 8px;
  width: 65%;
}

#footer .footer-top .footer-newsletter input[type="submit"] {
  background: #f82249;
  border: 0;
  width: 35%;
  padding: 6px 0;
  text-align: center;
  color: #fff;
  transition: 0.3s;
  cursor: pointer;
}

#footer .footer-top .footer-newsletter input[type="submit"]:hover {
  background: #e0072f;
}

#footer .copyright {
  text-align: center;
  padding-top: 30px;
}

#footer .credits {
  text-align: center;
  font-size: 13px;
  color: #ddd;
}

/*--------------------------------------------------------------
# Responsive Media Queries
--------------------------------------------------------------*/

@media (min-width: 767px) {
  #subscribe input {
    min-width: 400px;
  }
}

@media (min-width: 768px) {
  #contact .contact-address,
  #contact .contact-phone,
  #contact .contact-email {
    padding: 20px 0;
  }

  #contact .contact-phone {
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
  }
}

@media (min-width: 991px) {
  #schedule .sub-heading {
    width: 75%;
  }
}

@media (min-width: 1024px) {
  #intro {
    background-attachment: fixed;
  }

  #contest {
    background-attachment: fixed;
  }

  #subscribe {
    background-attachment: fixed;
  }
}

@media (max-width: 1199px) {
  #header .container {
    max-width: 100%;
  }

  .nav-menu a {
    padding: 8px 4px;
  }
}


@media (max-width: 991px) {
  #mobile-nav-toggle {
    position: fixed;
    right: 0;
    top: 10px;
    z-index: 999;
    margin: 15px;
    border: 0;
    background: none;
    font-size: 24px;
    display: block;
    transition: all 0.4s;
    outline: none;
    cursor: pointer;
  }

  #mobile-nav {
    top: 100px;
  }

  #logo {
    padding: 0.5rem 0;
  }

  #logo img {
    max-height: 3rem !important;
  }

  #logo.pull-left {
    margin-left: 0 !important;
  }

  #logo.pull-right {
    margin-right: 0 !important;
  }

  #header {
    background: rgba(255, 255, 255, 0.98);
    transition: all 0.5s;
  }

  #header .container {
    justify-content: space-between;
  }

  #nav-menu-container {
    display: none;
  }

  #mobile-nav-toggle {
    display: inline;
  }

  #intro .intro-container {
    top: 190px;
  }

  #intro h1 {
    font-size: 34px;
  }

  #intro p {
    font-size: 16px;
  }

  #schedule .nav-tabs a {
    padding: 8px 60px;
  }

  #announcement-banner {
    padding-right: calc(1rem + 32px) !important;
  }
}

@media (max-width: 768px) {
  .back-to-top {
    bottom: 15px;
  }

  #faq #faq-list a {
    font-size: 18px;
  }

  #faq #faq-list i {
    top: 13px;
  }
}

@media (max-width: 767px) {
  #schedule .nav-tabs a {
    padding: 8px 50px;
  }
}

@media (max-width: 574px) {
  #venue .venue-info h3 {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  #schedule .nav-tabs a {
    padding: 8px 30px;
  }
}

@media (max-width: 460px) {
  #subscribe button {
    margin-top: 10px;
  }
}

#sponsors {
  margin-bottom: 100px;
}

#sponsors .sponsors-wrap {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 40px;
}

#sponsors .sponsor-logo {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
  margin: 20px;
}

#sponsors .sponsor-logo img {
  max-width: 300px;
  object-fit: contain;
}

#logo.pull-left {
  margin-left: -20px;
}

#logo.pull-right {
  margin-right: -20px;
}

#announcement-banner {
  background-color: #0e1b4d;
  color: #ffffff;
  padding: 1rem;
  top: 0;
  width: 100%;
  z-index: 1050;
  text-align: center;
  font-size: 16px;

  p {
    margin: 0;
  }
}

#announcement-banner a {
  color: #ffffff;
  text-decoration: underline;
}

#announcement-banner a:hover {
  color: #ffd700;
}
#countdown-timer {
  font-size: 1rem;
  letter-spacing: 0.5px;
}

@media (max-width: 768px) {
  #countdown-timer {
    font-size: 0.95rem;
    padding: 1rem;
  }
}

.social-icons {
    position: fixed;
    top: 50%;
    right: 0;
    margin-top: -137.5px;
    display: flex;
    flex-direction: column;
    z-index: 1000;
}

.social-icon {
    display: block;
    width: 50px;
    height: 50px;
    background-color: #fff;
    color: #2f3138;
    text-align: center;
    line-height: 50px;
    font-size: 24px;
    margin-bottom: 5px;
    border-radius: 5px 0 0 5px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
}

.social-icon:hover {
    transform: translateX(-10px);
    color: #fff;
}

.social-icon.facebook:hover {
    background-color: #3b5998;
}

.social-icon.instagram:hover {
    background: #d6249f;
    background: radial-gradient(circle at 30% 107%, #fdf497 0%, #fdf497 5%, #fd5949 45%,#d6249f 60%,#285AEB 90%);
}

.social-icon.tiktok:hover {
    background-color: #000;
}

.social-icon.twitter:hover {
    background-color: #1da1f2;
}

.social-icon.web:hover {
    background-color: #f82249;
}
