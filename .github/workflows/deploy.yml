name: Deploy to S3 and Invalidate CloudFront

on:
  push:
    branches:
      - master

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Install AWS CLI
      run: |
        sudo apt-get update

    - name: Sync files to S3
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_REGION: us-east-1
      run: |
        aws s3 sync . s3://ioi2025-new-prod \
          --exclude "terraform-stuff/*" \
          --exclude ".github/*" \
          --exclude ".git/*" \
          --delete

    - name: Invalidate CloudFront Cache
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_REGION: us-east-1
      run: |
        aws cloudfront create-invalidation \
          --distribution-id E2ZAO5I0NASX7G \
          --paths "/*"
