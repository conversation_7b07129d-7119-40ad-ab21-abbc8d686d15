<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Home | IOI 2025 Bolivia</title>
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <meta content="" name="keywords">
  <meta content="" name="description">

  <!-- Favicons -->
  <link href="img/favicon-32x32.png" rel="icon">
  <link href="img/apple-touch-icon.png" rel="apple-touch-icon">

  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,700,700i|Raleway:300,400,500,700,800"
    rel="stylesheet">

  <link href="lib/bootstrap/css/bootstrap.min.css" rel="stylesheet">

  <link href="lib/font-awesome/css/font-awesome.min.css" rel="stylesheet">
  <link href="lib/animate/animate.min.css" rel="stylesheet">
  <link href="lib/venobox/venobox.css" rel="stylesheet">
  <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">

  <link href="css/style.css" rel="stylesheet">
</head>

<body>
  <!-- Header -->
  <header id="header">
    <!-- ======= Announcement banner ======= -->
    <div id="announcement-banner" class="text-center">
			<p class="mb-0 d-flex flex-column flex-md-row justify-content-center align-items-center gap-2">
				<span class="fw-semibold ">
			📢&nbsp;Registration is now closed. Thanks to everyone who registered.
        	</span>
			</p>
		</div>

    <div class="container d-flex align-items-center justify-content-between">

      <div id="logo">
        <a href="/" class="scrollto"><img src="img/ioi2025-short.png" alt="IOI 2025 Logo"></a>
      </div>

      <!-- Improved Nav Menu -->
      <nav id="nav-menu-container">
        <ul class="nav-menu">
          <li class="menu-has-children"><a href="#">IOI 2025</a>
            <ul>
              <li><a href="about-contest.html">About IOI</a></li>
              <li><a href="about-bolivia.html">About Bolivia</a></li>
              
              <li><a href="/gallery.html">Gallery</a></li>
              <li><a href="/news.html">News</a></li>
              <li><a href="/sponsors.html">Sponsors</a></li>
              <li><a href="/call-for-tasks.html">Call for Tasks</a></li>
            </ul>
          </li>

          <li class="menu-has-children"><a href="#">Competition</a>
            <ul>
              <li><a href="/contest-rules.html">Contest Rules</a></li>
              <li><a href="/grading-environment.html">Grading Environment</a></li>
              <li><a href="/competition-equipment.html">Competition Equipment</a></li>
              <li><a href="/tasks.html">Tasks</a></li>
            </ul>
          </li>

          <li class="menu-has-children"><a href="#">Schedule</a>
            <ul>
              <li><a href="schedule.html">Daily Schedule</a></li>
              <li><a href="ioi-conference.html">IOI Conference</a></li>
            </ul>
          </li>

          <li class="menu-has-children">
            <a href="#">Travel&nbsp;&amp;&nbsp;Visa</a>
            <ul>
              <li><a href="arrival-guidelines.html">Arrival&nbsp;Guidelines</a></li>
              <li><a href="inmigration-visa-information.html">Migration and Visa</a></li>
              
            </ul>
          </li>
          <li><a href="/accommodation.html">Accommodation</a></li>
          <li><a href="/gallery.html">Gallery</a></li>
          <li class="menu-has-children"><a href="#">News</a>
            <ul>
              <li><a href="/news.html">News IOI</a></li>
              <li><a href="/team-bolivia.html">Team Bolivia</a></li>
              <li><a href="/press-release.html">Press Release N° 1</a></li>
              <li><a href="/press-release-N2.html">Press Release N° 2</a></li>
              <li><a href="/press-release-N3.html">Press Release N° 3</a></li>
              <li><a href="/press-release-N4.html">Press Release N° 4</a></li>
              <li><a href="/press-release-N5.html">Press Release N° 5</a></li>
              <li><a href="/press-release-N6.html">Press Release N° 6</a></li>
            </ul>
          </li>
          <li><a href="/contact.html">Contact Us</a></li>
          <li><a href="/faq.html">FAQs</a></li>
        </ul>
      </nav>

      <div id="logo">
        <a href="#intro" class="scrollto"><img src="img/organizers/bicentenario.png" alt="Bicentenario Logo"></a>
      </div>

    </div>
  </header><!-- End Header -->
  <!--==========================
    Intro Section
  ============================-->

  <main id="main">
    <!--==========================
      Speakers Section
    ============================-->
    <section id="call4tasks-id" class="call4tasks wow fadeInUp">
      <div class="container">
        <div class="section-header">
          <h2>Call for Tasks&nbsp;IOI&nbsp;2025</h2>
        </div>
        <div class="section-header">
          <p>The IOI 2025 Scientific Committee is pleased to invite the IOI community to participate in
            the design of the IOI 2025 competition tasks.</p>

          <h3>Competition Tasks</h3>

          <p>IOI tasks generally focus on the design of efficient and correct algorithms. Input and
            output should be kept as simple as possible. The IOI needs both simple and difficult tasks that are
            creative. We are actively encouraging submissions of easy
            problems. While challenging problems are essential, we believe that including interesting problems with
            lower difficulty
            can be equally engaging. These problems should still require creative problem-solving skills and have
            interesting
            solutions. We especially encourage submissions of easy problems that can be solved in multiple ways.</p>

          <p>Previous IOI competition tasks can provide, for example, a good guide on the desired
            composition of tasks, the last 8 are listed below:</p>

          <ul>
            <li>The tasks from IOI 2024 may be found <a href="https://www.ioi2024.eg/competition-tasks">Here</a></li>
            <li>The tasks from IOI 2023 may be found <a href="https://ioinformatics.org/page/ioi-2023/58">Here</a></li>
            <li>The tasks from IOI 2022 may be found <a href="https://ioinformatics.org/page/ioi-2022/56">Here</a></li>
            <li>The tasks from IOI 2021 may be found <a href="https://ioinformatics.org/page/ioi-2021/55">Here</a></li>
            <li>The tasks from IOI 2020 may be found <a href="https://ioinformatics.org/page/ioi-2020/54">Here</a></li>
            <li>The tasks from IOI 2019 may be found <a href="https://ioinformatics.org/page/ioi-2019/51">Here</a></li>
            <li>The tasks from IOI 2018 may be found <a href="https://ioinformatics.org/page/ioi-2018/49">Here</a></li>
            <li>The tasks from IOI 2017 may be found <a href="https://ioinformatics.org/page/ioi-2017/43">Here</a></li>
          </ul>

          <p>A large collection can be found on the IOI site <a
              href="https://ioinformatics.org/page/contests/10">here</a>.</p>

          <p>However, the nature of past tasks should not restrict the design of new tasks; submissions of new types of
            tasks not yet
            seen at IOIs are encouraged. We are particularly interested in tasks whose basic rules (if not an optimal
            strategy) are
            accessible to a wide audience, and tasks that illustrate algorithms and computational problems arising in a
            variety of
            human endeavors. Open-ended tasks, those that do not necessarily have a known efficient or optimal solution,
            are
            welcome.</p>

          <p>We are also particularly interested in tasks that go beyond the typical format where a program collects
            input, performs
            some computation, and outputs results. Examples include "reactive" and "output-only" tasks that have
            occasionally been
            used in past IOIs. Tasks with some measure of solution effectiveness other than CPU time consumption are
            encouraged.</p>

          <p>A syllabus listing material generally considered acceptable for IOI tasks can be found
            <a href="https://ioinformatics.org/page/syllabus/12">here</a>. The syllabus is not meant to be restrictive,
            but rather to serve as
            a guideline for task preparation.
          </p>

          <p>To ensure a fair and interesting competition for everyone, the tasks must satisfy the following conditions:
          </p>

          <ul>
            <li>the tasks must not have been seen by any potential IOI 2025 contestants;</li>
            <li>the tasks must not have been used in any recent similar competition;</li>
            <li>the tasks must be solvable by IOI competitors during an IOI contest round;</li>
            <li>the task descriptions must be unambiguous and easy to understand;</li>
            <li>the tasks must be original and/or innovative.</li>
          </ul>
          <p>For further reading, the <a
              href="https://docs.google.com/document/u/1/d/e/2PACX-1vTWJcMI9iv0LjG4GyntNN5t_CRM1IHSYk3ItStvVpUuJJTaFo_QmTg5Rin7SDlKLCV6zPf1ji5lDAo1/pub">ISC
              procedures and policies</a>
            document contains a brief description of the task selection process applied by the ISC over the recent
            years.</p>

          <h3>Task criteria</h3>
          <p>A task submission must contain:</p>

          <ul>
            <li>Statement in English, preferably formatted in PDF with required diagrams and pictures included.</li>
            <li>Description of the desired solution (a description of an algorithm which should get full score).</li>
            <li>Contact address (preferably an email address) and background information on the task author(s):
              affiliation, country,
              and a description of the author's role in the IOI or national olympiad, including training duties, over
              the period from
              IOI 2023 to IOI 2025.</li>
            <li>If you want the ISC to provide feedback on your submission, you should also provide a PGP key ID along
              with the
              submission.</li>
          </ul>

          <b>It is also strongly recommended that it contains:</b>

          <ul>
            <li>At least one implementation of the desired solution in C++.</li>
            <li>Analysis of alternative solutions.</li>
            <li>Suggestions for grading.</li>
            <li>Test data or ideas for generating test data.</li>
            <li>The motivation behind the task.</li>
          </ul>

          <p>Any comments related to the task are also welcome and would be highly appreciated.</p>

          <p>Submitted tasks must be kept in strict confidence until the end of IOI 2025. After that, authors are free
            to do whatever
            they wish with the tasks, but may be asked to have them considered for IOI 2026, in which case strict
            confidence would
            have to be maintained through until IOI 2026.</p>

          <h3>Submission process</h3>

          <p>Task materials should be placed together in a single file (use .zip or .tgz for multiple files) and
            submitted online
            <a href="https://isc.ioinformatics.org/taskbox/">here</a>. When submitting a task, you will need to enter
            the authorization code
            "pacha", Do not encrypt your submission: the upload page will do it automatically.
          </p>


          <h3>Notes</h3>

          <p>Note that by submitting the task, the author asserts that he or she is authorized to grant, and does grant
            IOI, an
            exclusive license to use the material until the IOI 2025 has finished and a perpetual non-exclusive
            transferable license
            to reproduce the material. The author guarantees that the requirements of this call for submissions are met
            and that the
            materials will not be disclosed to any third party during the duration of the exclusive license.</p>

          <p>Authors of tasks and their collaborators must not use a submitted task, or a variant thereof, or techniques
            specific to
            that task, in any competition or training until the IOI 2025 has ended. If in doubt, contact the ISC. Note
            that we do
            not wish to prohibit authors of submitted tasks from participating in other competitions and training, but
            we ask that
            they take all necessary precautions to safeguard confidentiality.</p>

          <h3>What happens next</h3>

          <p>Receipt of submissions will be acknowledged via the contact address provided. The Scientific Committee will
            carefully
            review all submissions and select a shortlist of around 10 tasks, six of which will eventually be given in
            the IOI 2025
            competition.</p>

          <p>The authors of shortlisted tasks will be invited to attend IOI 2025 in Bolivia as guests (authors will be
            responsible
            for their travel, but IOI 2025 will cover their stay). However, they will not be told before the competition
            whether
            their tasks will be used in the actual contest or whether the Scientific Committee has substantially
            modified their
            tasks.</p>

          <p>Authors of all submitted tasks will receive feedback from the ISC. Authors of tasks included in the actual
            competition
            will also be acknowledged by having their name, affiliation, and country included on the official IOI
            website (unless
            specifically declined by them). Problems from tasks included in the actual competition will be available
            under a
            <a href="https://en.wikipedia.org/wiki/Creative_Commons_license">Creative Commons Attribution (CC-BY)
              license</a>.
          </p>


          <h3>Summary</h3>

          <ul>
            <li>Submission deadline: <s>December 29th, 2024</s> Saturday, 18 January 2025</li>
            <li>Language: English</li>
            <li>Text format: PDF preferred</li>
            <li>Multiple files for one task (e.g., diagrams, solution code): in a single zipped or tarred archive</li>
            <li>Multiple tasks: submit separately</li>
            <li>Minimum submission contents:</li>
            <ul>
              <li>author information: name, email, affiliation, country, and role in olympiad</li>
              <li>task statement</li>
              <li>description of the desired solution.</li>
            </ul>
            <li>Also recommended:</li>
            <ul>
              <li>solution implementations in C++</li>
              <li>suggestions for grading</li>
              <li>test data</li>
              <li>alternative or near-expected solutions</li>
              <li>general information about the task</li>
              <li>any other comments relevant to the task.</li>
            </ul>
            <li>Submission site: <a
                href="https://isc.ioinformatics.org/taskbox/">https://isc.ioinformatics.org/taskbox/</a>, the
              authorization
              code is "pacha".</li>
          </ul>

          Direct questions about the call for tasks to scientific committee email address <a
            href="mailto:<EMAIL>"><EMAIL></a>.

        </div>
      </div>

    </section>

  </main>


  <!--==========================
    Footer
  ============================-->
  <footer id="footer">
		<div class="footer-top">
			<div class="container">
				<div class="row">

					<div class="col-lg-5 col-md-6">
						<h4>About IOI 2025 Bolivia</h4>
						<p>The 37th International Olympiad in Informatics will be held in Sucre, the constitutional capital of Bolivia. We are excited to welcome the world's brightest young minds for a week of competition, friendship, and cultural discovery.</p>
					</div>

					<div class="col-lg-3 col-md-6 footer-links">
						<h4>Useful Links</h4>
						<ul class="list-unstyled">
							<li><i class="fas fa-angle-right"></i> <a href="index.html">Home</a></li>
							<li><i class="fas fa-angle-right"></i> <a href="about-bolivia.html">About Bolivia</a></li>
							<li><i class="fas fa-angle-right"></i> <a href="about-contest.html">About IOI</a></li>
							<li><i class="fas fa-angle-right"></i> <a href="schedule.html">Daily Schedule</a></li>
						</ul>
					</div>

					<div class="col-lg-4 col-md-6 footer-links">
						<h4>Contact Us</h4>
						<p>
							IOI 2025 Organizing Committee<br> Sucre, Bolivia<br><br>
							<strong>Email:</strong> <EMAIL>

              <br>
						</p>
					</div>

				</div>
			</div>
		</div>

		<div class="container">
			<div class="copyright">
				&copy; Copyright <strong><span>IOI 2025 Bolivia</span></strong>. All Rights Reserved
			</div>
		</div>
	</footer>

  <a href="#" class="back-to-top"><i class="fa fa-angle-up"></i></a>
  <div class="social-icons">
		<a href="https://is.gd/Dn3XNI" target="_blank" class="social-icon facebook"><i class="fab fa-facebook-f"></i></a>
		<a href="https://is.gd/4kj9B1" target="_blank" class="social-icon instagram"><i class="fab fa-instagram"></i></a>
		<a href="https://www.tiktok.com/@ioi_bolivia" target="_blank" class="social-icon tiktok"><i class="fab fa-tiktok"></i></a>
		<a href="https://is.gd/pSEDDs" target="_blank" class="social-icon twitter"><i class="fab fa-twitter"></i></a>
		<a href="https://ioi2025.bo/" target="_blank" class="social-icon web"><i class="fas fa-globe"></i></a>
	</div>
  <!-- JavaScript Libraries -->
  <script src="lib/jquery/jquery.min.js"></script>
  <script src="lib/jquery/jquery-migrate.min.js"></script>
  <script src="lib/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="lib/easing/easing.min.js"></script>
  <script src="lib/superfish/hoverIntent.js"></script>
  <script src="lib/superfish/superfish.min.js"></script>
  <script src="lib/wow/wow.min.js"></script>
  <script src="lib/venobox/venobox.min.js"></script>
  <script src="lib/owlcarousel/owl.carousel.min.js"></script>

  <!-- Contact Form JavaScript By Sam Gaus Sergio Rolo File -->
  <script src="contactform/contactform.js"></script>

  <!-- Template Main Javascript File -->
  <script src="js/main.js"></script>
</body>

</html>