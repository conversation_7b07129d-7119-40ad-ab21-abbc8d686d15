<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Press Release | IOI 2025 Bolivia</title>
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <meta content="" name="keywords">
  <meta content="" name="description">

  <!-- Favicons -->
  <link href="img/favicon-32x32.png" rel="icon">
  <link href="img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,700,700i|Raleway:300,400,500,700,800"
    rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="lib/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="lib/font-awesome/css/font-awesome.min.css" rel="stylesheet">
  <link href="lib/animate/animate.min.css" rel="stylesheet">
  <link href="lib/venobox/venobox.css" rel="stylesheet">
  <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
  <link rel="stylesheet" href="lib/aos/animate.min.css" />
  <link href="lib/aos/aos.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">


  <!-- Main CSS File -->
  <link href="/css/style.css" rel="stylesheet">

  <!-- Custom Press Release Styles -->
  <style>
    .article-content p {
      font-size: 1.1rem;
      line-height: 1.8;
      color: #333;
    }

    .article-content .lead {
      font-size: 1.2rem;
      font-weight: 400;
    }

    .press-release-header {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 2rem;
      margin-bottom: 2rem;
    }

    .government-seal {
      max-width: 100px;
      height: auto;
    }

    @media print {
      .press-release-header {
        background: white !important;
        border: 2px solid #000 !important;
      }

      body {
        font-family: 'Times New Roman', serif !important;
      }
    }

    .text-justify {
      text-align: justify;
    }

    hr.press-divider {
      border-top: 2px solid #333;
      width: 75%;
      margin: 2rem auto;
    }
  </style>
</head>

<body>

  <!-- Header -->
  <header id="header">
    <!-- ======= Announcement banner ======= -->
    <div id="announcement-banner" class="text-center">
			<p class="mb-0 d-flex flex-column flex-md-row justify-content-center align-items-center gap-2">
				<span class="fw-semibold ">
			📢&nbsp;Registration is now closed. Thanks to everyone who registered.
        	</span>
			</p>
		</div>

    <div class="container d-flex align-items-center justify-content-between">

      <div id="logo">
        <a href="/" class="scrollto"><img src="img/ioi2025-short.png" alt="IOI 2025 Logo"></a>
      </div>

      <!-- Improved Nav Menu -->
      <nav id="nav-menu-container">
        <ul class="nav-menu">
          <li class="menu-has-children"><a href="#">IOI 2025</a>
            <ul>
              <li><a href="about-contest.html">About IOI</a></li>
              <li><a href="about-bolivia.html">About Bolivia</a></li>
              
              <li><a href="/gallery.html">Gallery</a></li>
              <li><a href="/news.html">News</a></li>
              <li><a href="/sponsors.html">Sponsors</a></li>
              <li><a href="/call-for-tasks.html">Call for Tasks</a></li>
            </ul>
          </li>

          <li class="menu-has-children"><a href="#">Competition</a>
            <ul>
              <li><a href="/contest-rules.html">Contest Rules</a></li>
              <li><a href="/grading-environment.html">Grading Environment</a></li>
              <li><a href="/competition-equipment.html">Competition Equipment</a></li>
              <li><a href="/tasks.html">Tasks</a></li>
            </ul>
          </li>

          <li class="menu-has-children"><a href="#">Schedule</a>
            <ul>
              <li><a href="schedule.html">Daily Schedule</a></li>
              <li><a href="ioi-conference.html">IOI Conference</a></li>
            </ul>
          </li>

          <li class="menu-has-children">
            <a href="#">Travel&nbsp;&amp;&nbsp;Visa</a>
            <ul>
              <li><a href="arrival-guidelines.html">Arrival&nbsp;Guidelines</a></li>
              <li><a href="inmigration-visa-information.html">Migration and Visa</a></li>
              
            </ul>
          </li>
          <li><a href="/accommodation.html">Accommodation</a></li>
          <li><a href="/gallery.html">Gallery</a></li>
          <li class="menu-has-children"><a href="#">News</a>
            <ul>
              <li><a href="/news.html">News IOI</a></li>
              <li><a href="/team-bolivia.html">Team Bolivia</a></li>
              <li><a href="/press-release.html">Press Release N° 1</a></li>
              <li><a href="/press-release-N2.html">Press Release N° 2</a></li>
              <li><a href="/press-release-N3.html">Press Release N° 3</a></li>
              <li><a href="/press-release-N4.html">Press Release N° 4</a></li>
              <li><a href="/press-release-N5.html">Press Release N° 4</a></li>
              
            </ul>
          </li>
          <li><a href="/contact.html">Contact Us</a></li>
                    <li><a href="/faq.html">FAQs</a></li>
        </ul>
      </nav>

      <div id="logo">
        <a href="#intro" class="scrollto"><img src="img/organizers/bicentenario.png" alt="Bicentenario Logo"></a>
      </div>

    </div>
  </header><!-- End Header -->

  <!-- ======= Main ======= -->

  <main id="main">
    <div class="container py-5">

      <!-- Press Release Content -->
      <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8">

          <!-- Government Header -->
          <div class="press-release-header text-center">
            <div class="mb-4">
              <!-- Placeholder for government logos - you can replace with actual logo path -->
              <div class="d-flex justify-content-center align-items-center mb-3">
                <img src="img/organizers/bicentenario.png" alt="Bicentenario Logo" width="130px">
              </div>
            </div>
            <h5 class="text-uppercase font-weight-bold mb-2">ESTADO PLURINACIONAL DE BOLIVIA</h5>
            <h4 class="text-uppercase font-weight-bold mb-2 text-primary">MINISTERIO DE PLANIFICACIÓN PARA EL DESARROLLO</h4>
            <h6 class="text-uppercase font-weight-normal text-muted">VICEMINISTERIO DE CIENCIA Y TECNOLOGÍA</h6>
          </div>

          <!-- Main Headline -->
          <div class="mb-5">
            <h3 class="font-weight-bold text-center mb-4">
                Arrancan las IOI con 300 competidores que buscan innovar sistemas de programación informática para facilitar la vida diaria en el planeta
            </h3>

            <!-- Divider -->
            <div class="text-center mb-4">
              <img src="./img/index/img19.png" alt="" width="100%">
              <img src="./img/index/img20.png" alt="" width="100%">
              <img src="./img/index/img21.png" alt="" width="100%">
            </div>
          </div>

          <!-- Article Content -->
          <div class="article-content">
            <p class="lead text-justify mb-4">
                Más de 300 competidores provenientes de los cinco continentes del planeta arrancaron este miércoles las Olimpiadas Internacionales de Informática IOI 2025 en la ciudad de Sucre en busca de un objetivo común: innovar software y sistemas informáticos que facilitaran la vida diaria a millones de personas que viven en el planeta.
            </p>

            <p class="text-justify mb-4">
                Los jugadores, destacados estudiantes de escuela y colegios en el mundo, hábiles y diestros en programación competitiva, tendrán la oportunidad de proponer soluciones para automatizar tareas, resolver problemas y, sobre todo, diseñar nuevos códigos para gatillar la innovación tecnológica durante el Siglo XXI. 
            </p>

            <p class="text-justify mb-4">
                “Esencialmente levantarán los cimientos para desarrollar nuevas aplicaciones que servirán para crear nuevos sitios web, juegos, y herramientas que mejoran la eficiencia en diversos ámbitos del que hacer cotidiano del ser humano en el mundo”, enfatizó Rolando Troche Venegas, miembro del comité técnico internacional de la IOI 2025.
            </p>
            <p><b>Competencia al más alto nivel</b>    </p>

            <p class="text-justify mb-4">
                Al término de la primera jornada de competencia al Team Bolivia “superó varios obstáculos y, está comenzando a mostrar eficacia”, apuntó Rolando Troche, miembro responsable del comité técnico internacional de la IOI 2025.
            </p>

            <p class="text-justify mb-4">
                “Estamos comenzado a responder con fuerza y, sobre todo con inteligencia”, adelantó el especialista en una primera evaluación de los resultados preliminares que dejó la primera jornada de competencia en las IOI.
            </p>


            <p class="text-justify mb-4">
                Los juegos comenzaron a las 10:00 de la mañana en ambientes del Polideportivo de la Villa Bolivariana y, culminaron a las 15.00. Durante cinco horas ininterrumpidas, los competidores internacionales mostraron, inicialmente, que destrezas tienen y, que innovación en programación esperan hacer realidad.
            </p>
            <p class="text-justify mb-4">
                La segunda jornada de la IOI y, sesión definitiva para anunciar a los ganadores será este viernes 1 de agosto en el mismo horario y, bajo la misma presión de tiempo, es decir, cinco horas continuas y directas.
            </p>
            <p><b>Objetivos estratégicos alcanzar en la IOI 2025</b>    </p>

            <p class="text-justify mb-4">
                Cinco objetivos resumen los ejes estratégicos que la IOI 2025 deberá alcanzar y, sobre los cuales, los competidores internacionales, deberán mostrar destreza, innovación, pero sobre todo, eficiencia para resolver problemas estructurales de programación.
            </p>
            <p class="text-justify mb-4">
                Las áreas claves son: automatizar tareas, desarrollar algoritmos para un nuevo software; optimizar procesos; resolver problemas; innovación y creatividad; y finalmente, aunque no esta oficialmente anunciado, plantear soluciones para la aplicación de inteligencia artificial y aprendizaje automático, comentó Troche.
            </p>
            <p class="text-justify mb-4">
                La programación, por ejemplo, permite automatizar procesos repetitivos, liberando tiempo y reduciendo errores en diversas tareas, desde la gestión de archivos hasta el envío de correos electrónicos, mencionó el especialista.
            </p>
            <p class="text-justify mb-4">
                Otra área central de la competencia tendrá que ver con la creación de aplicaciones lógicas para dispositivos móviles, computadoras y otros dispositivos.
            </p>
            <p class="text-justify mb-4">
                Los jugadores aplicarán también lo mejor que tienen para mejorar códigos de programación para la eficiencia de sistemas y procesos en diferentes industrias, como la logística, la manufactura y las finanzas. 
            </p> 
            <p class="text-justify mb-4">
                Los problemas que resolverán los competidores tendrán que poner al descubierto también herramientas esenciales para analizar y resolver problemas complejos en diversos campos, desde la medicina hasta la ingeniería. 
            </p> 
            <p class="text-justify mb-4">
                Otro lenguaje que será evaluado al momento de calificar a los mejores durante el desarrollo de las Olimpiadas, tendrá que ver con materializar ideas innovadoras, creando nuevas soluciones y productos que impulsarán, a su vez, el progreso tecnológico de nueva generación, útil y efectiva, subrayó Troche.
            </p> 
            <p class="text-justify mb-4">
                Un asunto de importancia que las IOI podrían abrir en esta 37° Olimpiada tiene que ver con la inteligencia artificial y los procesos de aprendizaje automático. 
            </p> 
            <p class="text-justify mb-4">
                En este sentido, la programación es fundamental para el desarrollo de algoritmos y modelos que permiten a las máquinas aprender y tomar decisiones.  
            </p> 
            <p class="text-justify mb-4">
                “Aunque en está Olimpiada la Inteligencia Artificial está ausente, por el momento, no esta descartada que pueda sentarse las bases para que constituya otro campo fértil de competencia durante el desarrollo de la IOI 2026”, apuntó Troche.
            </p>   
            <p class="text-justify mb-4">
                “Aunque en está Olimpiada la Inteligencia Artificial está ausente, por el momento, no esta descartada que pueda sentarse las bases para que constituya otro campo fértil de competencia durante el desarrollo de la IOI 2026”, apuntó Troche.
            </p>   

            

            <div class="text-center mt-5">
              <small class="text-muted">
                Sucre, Bolivia - Julio 2025<br>
                Ministerio de Planificación para el Desarrollo<br>
                Viceministerio de Ciencia y Tecnología
              </small>
            </div>

            

          </div>

        </div>
      </div>

    </div>
  </main>


  <!-- ======= Footer By Sam Gaus Sergio Rolo ======= -->
  <footer id="footer">
		<div class="footer-top">
			<div class="container">
				<div class="row">

					<div class="col-lg-5 col-md-6">
						<h4>About IOI 2025 Bolivia</h4>
						<p>The 37th International Olympiad in Informatics will be held in Sucre, the constitutional capital of Bolivia. We are excited to welcome the world's brightest young minds for a week of competition, friendship, and cultural discovery.</p>
					</div>

					<div class="col-lg-3 col-md-6 footer-links">
						<h4>Useful Links</h4>
						<ul class="list-unstyled">
							<li><i class="fas fa-angle-right"></i> <a href="index.html">Home</a></li>
							<li><i class="fas fa-angle-right"></i> <a href="about-bolivia.html">About Bolivia</a></li>
							<li><i class="fas fa-angle-right"></i> <a href="about-contest.html">About IOI</a></li>
							<li><i class="fas fa-angle-right"></i> <a href="schedule.html">Daily Schedule</a></li>
						</ul>
					</div>

					<div class="col-lg-4 col-md-6 footer-links">
						<h4>Contact Us</h4>
						<p>
							IOI 2025 Organizing Committee<br> Sucre, Bolivia<br><br>
							<strong>Email:</strong> <EMAIL>

              <br>
						</p>
					</div>

				</div>
			</div>
		</div>

		<div class="container">
			<div class="copyright">
				&copy; Copyright <strong><span>IOI 2025 Bolivia</span></strong>. All Rights Reserved
			</div>
		</div>
	</footer>

  <a href="#" class="back-to-top"><i class="fa fa-angle-up"></i></a>
  <div class="social-icons">
		<a href="https://is.gd/Dn3XNI" target="_blank" class="social-icon facebook"><i class="fab fa-facebook-f"></i></a>
		<a href="https://is.gd/4kj9B1" target="_blank" class="social-icon instagram"><i class="fab fa-instagram"></i></a>
		<a href="https://www.tiktok.com/@ioi_bolivia" target="_blank" class="social-icon tiktok"><i class="fab fa-tiktok"></i></a>
		<a href="https://is.gd/pSEDDs" target="_blank" class="social-icon twitter"><i class="fab fa-twitter"></i></a>
		<a href="https://ioi2025.bo/" target="_blank" class="social-icon web"><i class="fas fa-globe"></i></a>
	</div>
  <!-- Vendor JS Files -->
  <script src="lib/jquery/jquery.min.js"></script>
  <script src="lib/jquery/jquery-migrate.min.js"></script>
  <script src="lib/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="lib/easing/easing.min.js"></script>
  <script src="lib/superfish/hoverIntent.js"></script>
  <script src="lib/superfish/superfish.min.js"></script>
  <script src="lib/wow/wow.min.js"></script>
  <script src="lib/venobox/venobox.min.js"></script>
  <script src="lib/owlcarousel/owl.carousel.min.js"></script>

  <!-- Template Main JS File -->
  <script src="js/main.js"></script>
  <script src="lib/aos/aos.js"></script>
  <script>
    AOS.init({
      duration: 1000,
      once: true
    });

    // Function to download as PDF (placeholder - would need a PDF generation library)
    function downloadPDF() {
      // This is a placeholder function. In a real implementation, you would use
      // a library like jsPDF or html2pdf to generate the PDF
      alert('Funcionalidad de descarga PDF en desarrollo. Por favor, use la opción de imprimir y seleccione "Guardar como PDF".');
    }

    // Improve print styles
    window.addEventListener('beforeprint', function() {
      document.body.classList.add('printing');
    });

    window.addEventListener('afterprint', function() {
      document.body.classList.remove('printing');
    });
  </script>

</body>

</html>