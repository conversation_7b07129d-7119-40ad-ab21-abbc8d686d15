variable "project" {
  type = string
  description = "Project name"
}

variable "environment" {
  type = string
  description = "Environment name"
}

variable "maintainer" {
  type = string
  description = "Name of the maintainer"
}

variable "description" {
  type = string
  description = "Project description"
}

variable "acm_certificate_arn" {
  type = string
  description = "ACM certificate ARN for Cloudfront distribution"
}

variable "aliases" {
  type = list(string)
  description = "List of domain aliases for Cloudfront distribution"
}

variable "domain_name" {
  type = string
  description = "Base domain name for all subdomains"
}

variable "route53_zone_id" {
  type = string
  description = "Route53 Hosted Zone"
}
