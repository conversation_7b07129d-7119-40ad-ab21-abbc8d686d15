locals {
  bucket_name = "${var.project}-${var.environment}"
  tags = {
    Name = local.bucket_name
    Environment = var.environment
    Maintainer = var.maintainer
  }

  aliases_final = [
    for alias in var.aliases: "${alias}.${var.domain_name}"
  ]
}

resource "aws_s3_bucket" "default" {
  bucket = local.bucket_name

  tags = merge(local.tags, {
    Description = var.description
  })

  force_destroy = true
}

resource "aws_s3_bucket_cors_configuration" "default" {
  bucket = aws_s3_bucket.default.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "HEAD"]
    allowed_origins = ["http*"]
    expose_headers  = ["x-amz-meta-custom-header", "ETag"]
    max_age_seconds = 3000
  }
}

resource "aws_cloudfront_origin_access_identity" "default" {
  comment = "Origina access identity for static site"
}

data "aws_iam_policy_document" "site_bucket_policy" {
  statement {
    actions   = ["s3:GetObject"]
    resources = ["${aws_s3_bucket.default.arn}/*"]

    principals {
      type        = "AWS"
      identifiers = [aws_cloudfront_origin_access_identity.default.iam_arn]
    }
  }
}

resource "aws_s3_bucket_policy" "site_bucket_policy" {
  bucket = aws_s3_bucket.default.id
  policy = data.aws_iam_policy_document.site_bucket_policy.json
}

resource "aws_cloudfront_distribution" "default" {
  origin {
    domain_name = aws_s3_bucket.default.bucket_regional_domain_name
    origin_id   = aws_s3_bucket.default.id

    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.default.cloudfront_access_identity_path
    }
  }

  aliases = local.aliases_final

  default_cache_behavior {
    viewer_protocol_policy = "redirect-to-https"

    allowed_methods  = ["GET", "HEAD"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = aws_s3_bucket.default.id

    forwarded_values {
      query_string = true

      cookies {
        forward = "all"
      }
    }
    compress    = true
    min_ttl     = 0
    default_ttl = 86400
    max_ttl     = 31536000
  }

  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "index.html"

  price_class = "PriceClass_All"

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  tags = merge(local.tags, {
    Description = var.description
  })

  viewer_certificate {
    cloudfront_default_certificate = false
    ssl_support_method             = "sni-only"
    acm_certificate_arn            = var.acm_certificate_arn
  }

  custom_error_response {
    error_code            = 403
    response_code         = 200
    response_page_path    = "/index.html"
    error_caching_min_ttl = 300
  }

  custom_error_response {
    error_code            = 404
    response_code         = 200
    response_page_path    = "/index.html"
    error_caching_min_ttl = 300
  }
}

resource "aws_route53_record" "default" {
  count   = var.route53_zone_id == "" ? 0 : length(var.aliases)

  zone_id = var.route53_zone_id
  name    = var.aliases[count.index]
  type    = "CNAME"
  ttl     = 300
  records = [
    aws_cloudfront_distribution.default.domain_name
  ]
}
