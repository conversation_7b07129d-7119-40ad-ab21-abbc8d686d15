<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Press Release | IOI 2025 Bolivia</title>
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <meta content="" name="keywords">
  <meta content="" name="description">

  <!-- Favicons -->
  <link href="img/favicon-32x32.png" rel="icon">
  <link href="img/apple-touch-icon.png" rel="apple-touch-icon">

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,700,700i|Raleway:300,400,500,700,800"
    rel="stylesheet">

  <!-- Vendor CSS Files -->
  <link href="lib/bootstrap/css/bootstrap.min.css" rel="stylesheet">
  <link href="lib/font-awesome/css/font-awesome.min.css" rel="stylesheet">
  <link href="lib/animate/animate.min.css" rel="stylesheet">
  <link href="lib/venobox/venobox.css" rel="stylesheet">
  <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet">
  <link rel="stylesheet" href="lib/aos/animate.min.css" />
  <link href="lib/aos/aos.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">


  <!-- Main CSS File -->
  <link href="/css/style.css" rel="stylesheet">

  <!-- Custom Press Release Styles -->
  <style>
    .article-content p {
      font-size: 1.1rem;
      line-height: 1.8;
      color: #333;
    }

    .article-content .lead {
      font-size: 1.2rem;
      font-weight: 400;
    }

    .press-release-header {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 2rem;
      margin-bottom: 2rem;
    }

    .government-seal {
      max-width: 100px;
      height: auto;
    }

    @media print {
      .press-release-header {
        background: white !important;
        border: 2px solid #000 !important;
      }

      body {
        font-family: 'Times New Roman', serif !important;
      }
    }

    .text-justify {
      text-align: justify;
    }

    hr.press-divider {
      border-top: 2px solid #333;
      width: 75%;
      margin: 2rem auto;
    }
  </style>
</head>

<body>

  <!-- Header -->
  <header id="header">
    <!-- ======= Announcement banner ======= -->
    <div id="announcement-banner" class="text-center">
			<p class="mb-0 d-flex flex-column flex-md-row justify-content-center align-items-center gap-2">
				<span class="fw-semibold ">
			📢&nbsp;Registration is now closed. Thanks to everyone who registered.
        	</span>
			</p>
		</div>

    <div class="container d-flex align-items-center justify-content-between">

      <div id="logo">
        <a href="/" class="scrollto"><img src="img/ioi2025-short.png" alt="IOI 2025 Logo"></a>
      </div>

      <!-- Improved Nav Menu -->
      <nav id="nav-menu-container">
        <ul class="nav-menu">
          <li class="menu-has-children"><a href="#">IOI 2025</a>
            <ul>
              <li><a href="about-contest.html">About IOI</a></li>
              <li><a href="about-bolivia.html">About Bolivia</a></li>
            
              <li><a href="/gallery.html">Gallery</a></li>
              <li><a href="/news.html">News</a></li>
              <li><a href="/sponsors.html">Sponsors</a></li>
              <li><a href="/call-for-tasks.html">Call for Tasks</a></li>
            </ul>
          </li>

          <li class="menu-has-children"><a href="#">Competition</a>
            <ul>
              <li><a href="/contest-rules.html">Contest Rules</a></li>
              <li><a href="/grading-environment.html">Grading Environment</a></li>
              <li><a href="/competition-equipment.html">Competition Equipment</a></li>
              <li><a href="/tasks.html">Tasks</a></li>
            </ul>
          </li>

          <li class="menu-has-children"><a href="#">Schedule</a>
            <ul>
              <li><a href="schedule.html">Daily Schedule</a></li>
              <li><a href="ioi-conference.html">IOI Conference</a></li>
            </ul>
          </li>

          <li class="menu-has-children">
            <a href="#">Travel&nbsp;&amp;&nbsp;Visa</a>
            <ul>
              <li><a href="arrival-guidelines.html">Arrival&nbsp;Guidelines</a></li>
              <li><a href="inmigration-visa-information.html">Migration and Visa</a></li>
              
            </ul>
          </li>
          <li><a href="/accommodation.html">Accommodation</a></li>
          <li><a href="/gallery.html">Gallery</a></li>
          <li class="menu-has-children"><a href="#">News</a>
            <ul>
              <li><a href="/news.html">News IOI</a></li>
              <li><a href="/team-bolivia.html">Team Bolivia</a></li>
              <li><a href="/press-release.html">Press Release N° 1</a></li>
              <li><a href="/press-release-N2.html">Press Release N° 2</a></li>
              <li><a href="/press-release-N3.html">Press Release N° 3</a></li>
              <li><a href="/press-release-N4.html">Press Release N° 4</a></li>
              <li><a href="/press-release-N5.html">Press Release N° 5</a></li>
              <li><a href="/press-release-N6.html">Press Release N° 6</a></li>
            </ul>
          </li>
          <li><a href="/contact.html">Contact Us</a></li>
          <li><a href="/faq.html">FAQs</a></li>
        </ul>
      </nav>

      <div id="logo">
        <a href="#intro" class="scrollto"><img src="img/organizers/bicentenario.png" alt="Bicentenario Logo"></a>
      </div>

    </div>
  </header><!-- End Header -->

  <!-- ======= Main ======= -->

  <main id="main">
    <div class="container py-5">

      <!-- Press Release Content -->
      <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8">

          <!-- Government Header -->
          <div class="press-release-header text-center">
            <div class="mb-4">
              <!-- Placeholder for government logos - you can replace with actual logo path -->
              <div class="d-flex justify-content-center align-items-center mb-3">
                <img src="img/organizers/bicentenario.png" alt="Bicentenario Logo" width="130px">
              </div>
            </div>
            <h5 class="text-uppercase font-weight-bold mb-2">ESTADO PLURINACIONAL DE BOLIVIA</h5>
            <h4 class="text-uppercase font-weight-bold mb-2 text-primary">MINISTERIO DE PLANIFICACIÓN PARA EL DESARROLLO</h4>
            <h6 class="text-uppercase font-weight-normal text-muted">VICEMINISTERIO DE CIENCIA Y TECNOLOGÍA</h6>
          </div>

          <!-- Press Release Title -->
          <div class="text-center mb-5">
            <hr class="press-divider">
            <h2 class="text-uppercase font-weight-bold mb-4"> NOTA DE PRENSA </h2>
            <hr class="press-divider">
          </div>

          <!-- Main Headline -->
          <div class="mb-5">
            <h3 class="font-weight-bold text-center mb-4">
              Destacados jóvenes en tecnología de 98 países estarán en Bolivia para competir en la XXXVII Olimpiada Internacional de Informática
            </h3>

            <!-- Divider -->
            <div class="text-center mb-4">
              <img src="./img/index/img15.png" alt="">
            </div>
          </div>

          <!-- Article Content -->
          <div class="article-content">
            <p class="lead text-justify mb-4">
              Durante una semana <em>-del 27 de julio al 3 de agosto próximos-</em> la ciudad de Sucre acogerá a delegaciones internacionales de más de 90 países para participar de la 37° Olimpiada Internacional de Informática (IOI 2025).
            </p>

            <p class="text-justify mb-4">
                Será la primera vez que Bolivia será sede de un evento internacional de informática gran envergadura, del cual participarán más de mil personas a través de la presencia activa de delegaciones, comités internacionales, invitados especiales y, colaboradores voluntarios.
            </p>

            <p class="text-justify mb-4">
                El certamen internacional volverá de nuevo al continente después del año 1993 cuando Argentina organizó el evento mundial en la provincia de Mendoza.
            </p>

            <p class="text-justify mb-4">
                El IOI se organiza anualmente por uno de los países participantes. Cada nación suele enviar una delegación de cuatro concursantes y dos líderes. 
            </p>

            <p class="text-justify mb-4">
                El último país anfitrión de la IOI fue Egipto en septiembre de 2024. Alejandría, la mítica ciudad antigua fue sede de la versión 36 de la justa olímpica que ahora, prenderá la antorcha del conocimiento en la ciudad de los cuatro nombres: Sucre, donde además el continente americano alumbró la independencia de Bolivia del yugo español durante la época colonial.
            </p>

            <p class="text-justify mb-4">
                La nación Euroasiática de Uzbekistán será la sede de la versión 38 el próximo año y, Alemania tendrá el privilegio de abrazar la IOI en 2027, oficializaron los organizadores.
            </p>
            <p><b>Innovación tecnológica y destrezas científicas</b>    </p>

            <p class="text-justify mb-4">
                Durante el desarrollo del certamen, los estudiantes desplegarán habilidades científicas individualmente e intentarán maximizar su puntuación resolviendo un conjunto de problemas informáticos durante dos días de competición. 
            </p>
            <p class="text-justify mb-4">
                Las tareas del concurso son de naturaleza algorítmica; sin embargo, los concursantes deben demostrar habilidades informáticas básicas como análisis de problemas, diseño de algoritmos y estructuras de datos, programación y pruebas. Los ganadores del IOI se encuentran entre los mejores jóvenes informáticos del mundo.
            </p>
            <p class="text-justify mb-4">
                Autoridades nacionales del VCT señalaron que la IOI es un evento que forma parte de las actividades de conmemoración del Bicentenario de la Independencia de Bolivia y representa una oportunidad para que, en el marco de su organización, el gobierno nacional a través del Ministerio de Planificación para el Desarrollo, exploré la incorporación de nuevos talentos bolivianos en innovación tecnológica, a las actividades económicas y sociales con la participación equitativa de género, la integración regional y el intercambio de conocimientos para contribuir, de forma efectiva, al desarrollo nacional.
            </p>
            <p class="text-justify mb-4">
                Bolivia llevará a cabo, en este sentido, diversas acciones destinadas a garantizar el éxito de esta justa científica que tendrá a la competencia como evento central. 
            </p>
            <p class="text-justify mb-4">
                Otras actividades relacionadas con el evento mundial serán la promoción de espacios de confraternización e integración de los participantes, así como la inauguración y clausura de la 37°OIO 2025, que tendrá bajo su responsabilidad el VCT.
            </p>  
            <p class="text-justify mb-4">
                Los organizadores anticiparon de antemano que está garantizada la logística orientada a la recepción de las delegaciones, hospedaje, transporte, alimentación entre otros y el cuidado general de cada uno de los miembros de las comitivas internacionales que participarán del evento mundial.
            </p>   
            <p class="text-justify mb-4">
                <b>La OIO en el mundo</b>
            </p> 
            <p class="text-justify mb-4">
                La propuesta de organizar olimpiadas internacionales de informática para escolares fue planteada en la 24.ª Conferencia General de la Organización de las Naciones Unidas para la Educación, la Ciencia y la Cultura (UNESCO) en París por el delegado búlgaro, profesor Blagovest Sendov, en octubre de 1987. 
            </p>  
            <p class="text-justify mb-4">
                Este plan se incluyó en el Quinto Programa Principal de la UNESCO para el bienio 1988-1989 (Sección 05 215).  En mayo de 1989, la UNESCO inició y patrocinó la primera Olimpiada Internacional de Informática (IOI). (Citado del Informe IOI'89 e IOI'92). La primera IOI se celebró en Bulgaria en 1989.
            </p>  
             

            <div class="mt-5 pt-4 border-top">
              <p class="text-justify mb-2">
                <strong>Para más información:</strong>
              </p>
              <p class="mb-1">
                <strong>Contacto de prensa:</strong> Ministerio de Planificación para el Desarrollo
              </p>
              <p class="mb-1">
                <strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a>
              </p>
              <p class="mb-1">
                <strong>Sitio web oficial:</strong> <a href="https://ioi2025.bo" target="_blank">https://ioi2025.bo</a>
              </p>
            </div>

            <div class="text-center mt-5">
              <small class="text-muted">
                Sucre, Bolivia - Julio 2025<br>
                Ministerio de Planificación para el Desarrollo<br>
                Viceministerio de Ciencia y Tecnología
              </small>
            </div>

            

          </div>

        </div>
      </div>

    </div>
  </main>


  <!-- ======= Footer By Sam Gaus Sergio Rolo ======= -->
  <footer id="footer">
		<div class="footer-top">
			<div class="container">
				<div class="row">

					<div class="col-lg-5 col-md-6">
						<h4>About IOI 2025 Bolivia</h4>
						<p>The 37th International Olympiad in Informatics will be held in Sucre, the constitutional capital of Bolivia. We are excited to welcome the world's brightest young minds for a week of competition, friendship, and cultural discovery.</p>
					</div>

					<div class="col-lg-3 col-md-6 footer-links">
						<h4>Useful Links</h4>
						<ul class="list-unstyled">
							<li><i class="fas fa-angle-right"></i> <a href="index.html">Home</a></li>
							<li><i class="fas fa-angle-right"></i> <a href="about-bolivia.html">About Bolivia</a></li>
							<li><i class="fas fa-angle-right"></i> <a href="about-contest.html">About IOI</a></li>
							<li><i class="fas fa-angle-right"></i> <a href="schedule.html">Daily Schedule</a></li>
						</ul>
					</div>

					<div class="col-lg-4 col-md-6 footer-links">
						<h4>Contact Us</h4>
						<p>
							IOI 2025 Organizing Committee<br> Sucre, Bolivia<br><br>
							<strong>Email:</strong> <EMAIL>

              <br>
						</p>
					</div>

				</div>
			</div>
		</div>

		<div class="container">
			<div class="copyright">
				&copy; Copyright <strong><span>IOI 2025 Bolivia</span></strong>. All Rights Reserved
			</div>
		</div>
	</footer>

  <a href="#" class="back-to-top"><i class="fa fa-angle-up"></i></a>
  <div class="social-icons">
		<a href="https://is.gd/Dn3XNI" target="_blank" class="social-icon facebook"><i class="fab fa-facebook-f"></i></a>
		<a href="https://is.gd/4kj9B1" target="_blank" class="social-icon instagram"><i class="fab fa-instagram"></i></a>
		<a href="https://www.tiktok.com/@ioi_bolivia" target="_blank" class="social-icon tiktok"><i class="fab fa-tiktok"></i></a>
		<a href="https://is.gd/pSEDDs" target="_blank" class="social-icon twitter"><i class="fab fa-twitter"></i></a>
		<a href="https://ioi2025.bo/" target="_blank" class="social-icon web"><i class="fas fa-globe"></i></a>
	</div>
  <!-- Vendor JS Files -->
  <script src="lib/jquery/jquery.min.js"></script>
  <script src="lib/jquery/jquery-migrate.min.js"></script>
  <script src="lib/bootstrap/js/bootstrap.bundle.min.js"></script>
  <script src="lib/easing/easing.min.js"></script>
  <script src="lib/superfish/hoverIntent.js"></script>
  <script src="lib/superfish/superfish.min.js"></script>
  <script src="lib/wow/wow.min.js"></script>
  <script src="lib/venobox/venobox.min.js"></script>
  <script src="lib/owlcarousel/owl.carousel.min.js"></script>

  <!-- Template Main JS File -->
  <script src="js/main.js"></script>
  <script src="lib/aos/aos.js"></script>
  <script>
    AOS.init({
      duration: 1000,
      once: true
    });

    // Function to download as PDF (placeholder - would need a PDF generation library)
    function downloadPDF() {
      // This is a placeholder function. In a real implementation, you would use
      // a library like jsPDF or html2pdf to generate the PDF
      alert('Funcionalidad de descarga PDF en desarrollo. Por favor, use la opción de imprimir y seleccione "Guardar como PDF".');
    }

    // Improve print styles
    window.addEventListener('beforeprint', function() {
      document.body.classList.add('printing');
    });

    window.addEventListener('afterprint', function() {
      document.body.classList.remove('printing');
    });
  </script>

</body>

</html>